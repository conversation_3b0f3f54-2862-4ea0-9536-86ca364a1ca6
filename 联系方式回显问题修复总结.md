# 社区互助编辑时联系方式回显问题修复

## 问题描述
用户编辑社区互助时，进入编辑界面后联系方式没有回显，导致用户无法看到当前设置的联系方式。

## 问题分析
通过对比商品发布页面（idle-publish）的联系方式处理逻辑，发现社区互助发布页面缺少统一的联系方式数据处理机制。

## 修复内容

### 1. 联系方式管理组件 (contact-manager/index.js)
- **添加数据观察器**：监听 `contactInfo` 属性变化，确保组件内部状态与外部数据同步
- **改进弹窗初始化**：
  - `showWechatDialog` 方法确保使用最新数据初始化微信号输入框
  - `showWechatQrDialog` 方法确保使用最新数据初始化微信二维码预览
- **添加生命周期方法**：`attached` 方法用于调试组件初始化
- **添加辅助方法**：`_updateQrPreview` 方法处理二维码预览更新

### 2. 社区互助发布页面 (community-help-publish/index.js)
- **参考商品发布页面实现**：添加 `parseContactInfo` 方法统一处理联系方式数据
- **统一数据解析**：使用 `ContactUtils.parseContactInfoFromJson` 方法解析联系方式
- **修改数据加载流程**：在 `loadHelpDetail` 方法中调用 `parseContactInfo` 方法
- **确保数据同步**：编辑模式下正确设置 `contactInfo` 数据并触发组件更新

## 关键修改点

### 联系方式管理组件
```javascript
// 添加数据观察器
observers: {
  'contactInfo': function(contactInfo) {
    console.log('联系方式数据更新:', contactInfo)
    
    // 同步更新微信号输入框
    if (contactInfo && this.data.showWechatDialog) {
      this.setData({
        wechatField: contactInfo.wechatId || ''
      })
    }
    
    // 同步更新微信二维码预览
    if (contactInfo && this.data.showWechatQrDialog && contactInfo.wechatQr) {
      this._updateQrPreview(contactInfo.wechatQr)
    }
  }
}

// 改进弹窗初始化
showWechatDialog() {
  const currentContactInfo = this.properties.contactInfo || {}
  this.setData({
    showWechatDialog: true,
    wechatField: currentContactInfo.wechatId || ''
  })
}
```

### 社区互助发布页面
```javascript
// 添加联系方式解析方法
parseContactInfo(contactInfoJson) {
  const contactInfo = ContactUtils.parseContactInfoFromJson(contactInfoJson)
  this.setData({
    contactInfo,
  })
  this.initFormValidation()
}

// 在数据加载完成后调用
async loadHelpDetail(helpId) {
  // ... 其他数据处理
  
  // 解析并适配联系方式信息
  this.parseContactInfo(helpDetail.contactInfo)
}
```

## 测试验证

### 测试步骤
1. 确保用户已设置联系方式（手机号、微信号、微信二维码）
2. 发布一条社区互助信息
3. 进入社区互助详情页面
4. 点击"编辑"按钮进入编辑页面
5. 点击"联系方式"选项
6. 验证联系方式数据是否正确回显

### 预期结果
- 所有联系方式数据应该正确回显
- 点击编辑微信号时，输入框应该显示当前的微信号
- 点击编辑微信二维码时，应该显示当前的二维码图片
- 各联系方式的可见性开关状态应该正确

## 技术要点

1. **数据同步机制**：通过 observers 确保组件内部状态与外部数据保持同步
2. **统一数据处理**：使用 ContactUtils 工具类统一处理联系方式数据格式
3. **参考成功实现**：借鉴商品发布页面的成功经验，保持代码一致性
4. **生命周期管理**：正确处理组件初始化和数据更新的时机

## 注意事项
- 修改后需要重新编译小程序
- 建议在开发者工具中清除缓存后测试
- 如果问题仍然存在，检查控制台是否有错误信息
- 确保 ContactUtils 工具类的方法正常工作
