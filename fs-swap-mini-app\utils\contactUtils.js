const util = require("./util")
const api = require("../config/api")
const ContactType = require('./ContactType.js')

/**
 * 联系方式工具类
 * 统一处理联系方式相关的逻辑
 */
class ContactUtils {
  
  /**
   * 获取用户联系方式
   * @returns {Promise<Object>} 联系方式对象
   */
  static async fetchUserContacts() {
    try {
      const res = await api.getUserContacts()
      if (res.code === 200 && res.data) {
        return this.parseContactsFromApi(res.data)
      }
      return this.getEmptyContactInfo()
    } catch (err) {
      console.error('获取联系方式失败:', err)
      return this.getEmptyContactInfo()
    }
  }

  /**
   * 解析API返回的联系方式数据
   * @param {Array} contacts API返回的联系方式数组
   * @returns {Object} 格式化的联系方式对象
   */
  static parseContactsFromApi(contacts) {
    let contactInfo = this.getEmptyContactInfo()

    contacts.forEach(contact => {
      const contactType = parseInt(contact.contactType)
      if (contactType === ContactType.MOBILE.code) {
        contactInfo.mobile = contact.contactValue
        contactInfo.mobileVisible = contact.isVisible
      } else if (contactType === ContactType.WECHAT_ID.code) {
        contactInfo.wechatId = contact.contactValue
        contactInfo.wechatVisible = contact.isVisible
      } else if (contactType === ContactType.WECHAT_QR.code) {
        if (contact.contactValue) {
          contactInfo.wechatQr = util.formatImageUrl(contact.contactValue)
        }
        contactInfo.wechatQrVisible = contact.isVisible
      }
    })

    return contactInfo
  }

  /**
   * 解析JSON格式的联系方式信息（用于编辑模式）
   * @param {string} contactInfoJson JSON字符串
   * @returns {Object} 格式化的联系方式对象
   */
  static parseContactInfoFromJson(contactInfoJson) {
    console.log('ContactUtils.parseContactInfoFromJson 输入:', contactInfoJson, '类型:', typeof contactInfoJson)
    if (!contactInfoJson) {
      console.log('ContactUtils.parseContactInfoFromJson: 输入为空，返回空联系方式')
      return this.getEmptyContactInfo()
    }

    try {
      // 如果输入已经是对象，直接使用；如果是字符串，则解析
      const contacts = typeof contactInfoJson === 'string' ? JSON.parse(contactInfoJson) : contactInfoJson
      console.log('ContactUtils.parseContactInfoFromJson 解析后的数组:', contacts)
      let contactInfo = this.getEmptyContactInfo()

      contacts.forEach(contact => {
        console.log('处理联系方式项:', contact)
        const contactType = parseInt(contact.type)
        if (contactType === ContactType.MOBILE.code) {
          contactInfo.mobile = contact.value
          contactInfo.mobileVisible = contact.visible
          console.log('设置手机号:', contact.value, '可见性:', contact.visible)
        } else if (contactType === ContactType.WECHAT_ID.code) {
          contactInfo.wechatId = contact.value
          contactInfo.wechatVisible = contact.visible
          console.log('设置微信号:', contact.value, '可见性:', contact.visible)
        } else if (contactType === ContactType.WECHAT_QR.code) {
          if (contact.value) {
            contactInfo.wechatQr = util.formatImageUrl(contact.value)
          }
          contactInfo.wechatQrVisible = contact.visible
          console.log('设置微信二维码:', contact.value, '可见性:', contact.visible)
        }
      })

      console.log('ContactUtils.parseContactInfoFromJson 最终结果:', contactInfo)
      return contactInfo
    } catch (error) {
      console.error('解析联系方式信息失败:', error)
      return this.getEmptyContactInfo()
    }
  }

  /**
   * 将联系方式对象转换为提交格式的JSON数组
   * @param {Object} contactInfo 联系方式对象
   * @returns {string} JSON字符串
   */
  static formatContactInfoForSubmit(contactInfo) {
    const contactData = []

    // 添加手机号联系方式
    if (contactInfo.mobile) {
      contactData.push({
        type: ContactType.MOBILE.code,
        value: contactInfo.mobile,
        visible: contactInfo.mobileVisible
      })
    }

    // 添加微信号联系方式
    if (contactInfo.wechatId) {
      contactData.push({
        type: ContactType.WECHAT_ID.code,
        value: contactInfo.wechatId,
        visible: contactInfo.wechatVisible
      })
    }

    // 添加微信二维码联系方式
    if (contactInfo.wechatQr) {
      contactData.push({
        type: ContactType.WECHAT_QR.code,
        value: contactInfo.wechatQr,
        visible: contactInfo.wechatQrVisible
      })
    }

    return JSON.stringify(contactData)
  }

  /**
   * 验证联系方式是否有效
   * @param {Object} contactInfo 联系方式对象
   * @returns {boolean} 是否有效
   */
  static validateContactInfo(contactInfo) {
    return (contactInfo.mobile && contactInfo.mobileVisible) ||
           (contactInfo.wechatId && contactInfo.wechatVisible) ||
           (contactInfo.wechatQr && contactInfo.wechatQrVisible)
  }

  /**
   * 获取空的联系方式对象
   * @returns {Object} 空的联系方式对象
   */
  static getEmptyContactInfo() {
    return {
      mobile: '',
      wechatId: '',
      wechatQr: '',
      mobileVisible: false,
      wechatVisible: false,
      wechatQrVisible: false
    }
  }

  /**
   * 检查联系方式是否为空
   * @param {Object} contactInfo 联系方式对象
   * @returns {boolean} 是否为空
   */
  static isContactInfoEmpty(contactInfo) {
    return !contactInfo.mobile && !contactInfo.wechatId && !contactInfo.wechatQr
  }

  /**
   * 获取可见的联系方式数量
   * @param {Object} contactInfo 联系方式对象
   * @returns {number} 可见的联系方式数量
   */
  static getVisibleContactCount(contactInfo) {
    let count = 0
    if (contactInfo.mobile && contactInfo.mobileVisible) count++
    if (contactInfo.wechatId && contactInfo.wechatVisible) count++
    if (contactInfo.wechatQr && contactInfo.wechatQrVisible) count++
    return count
  }

  /**
   * 获取联系方式的显示文本
   * @param {Object} contactInfo 联系方式对象
   * @returns {string} 显示文本
   */
  static getContactDisplayText(contactInfo) {
    const visibleCount = this.getVisibleContactCount(contactInfo)
    if (visibleCount === 0) {
      return '请设置联系方式'
    }
    return `已设置${visibleCount}种联系方式`
  }
}

module.exports = ContactUtils
