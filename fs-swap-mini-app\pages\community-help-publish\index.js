const api = require('../../config/api.js')
const systemInfoService = require('../../services/systemInfo')
const ContactType = require('../../utils/ContactType.js')
const ContactUtils = require('../../utils/contactUtils.js')
const util = require('../../utils/util')
const dateUtil = require('../../utils/dateUtil')

Page({
    data: {
        // 常量配置
        MAX_UPLOAD_COUNT: 6,
        MAX_CONTENT_LENGTH: 500,

        // 表单数据
        formData: {
            title: '',
            content: '',
            category: '',
            publishType: '',
            endTime: '',
        },

        // 图片列表
        fileList: [],

        // 分类相关
        selectedCategory: {},
        showCategoryPicker: false,
        categoryColumns: [],
        requireCategories: [],
        serviceCategories: [],

        // 发布类型相关
        publishTypes: [],
        selectedPublishType: {},
        showPublishTypePicker: false,
        publishTypeColumns: [],

        // 联系方式相关数据
        ContactType: ContactType,
        contactInfo: ContactUtils.getEmptyContactInfo(),

        // 联系方式弹窗状态
        showContactPicker: false,

        // 提交状态
        submitLoading: false,
        canSubmit: false,
        hasValidContact: false,

        // 发布确认弹窗
        showPublishDialog: false,

        // 截止时间相关
        showEndTimePicker: false,
        endTimeValue: new Date().getTime(),
        endTimeDisplay: '',
        minDate: new Date().getTime(),

        // 预设发布类型
        presetPublishType: '',

        // 预设分类
        presetCategory: '',

        // 编辑模式相关
        isEdit: false,
        helpId: null,

        // 计算属性
        titleLength: 0,

        // 内容自动填充控制
        isContentAutoFilled: false, // 标记content是否是自动填充的
        userModifiedContent: false, // 标记用户是否手动修改过content
    },

    onLoad(options) {
        console.log('发布互助页面加载', options)

        // 检查是否是编辑模式
        if (options && options.id && options.mode === 'edit') {
            this.setData({
                isEdit: true,
                helpId: options.id,
            })

            wx.setNavigationBarTitle({
                title: '编辑互助',
            })

            // 编辑模式：先加载分类和发布类型，再加载互助详情
            this.loadCategories().then(() => {
                this.loadPublishTypes().then(() => {
                    this.loadHelpDetail(options.id)
                })
            })
        } else {
            // 新增模式
            // 如果有预设的发布类型，设置到表单数据中
            if (options && options.publishType) {
                // 保持字符串类型
                const publishTypeStr = options.publishType.toString()

                this.setData({
                    'formData.publishType': publishTypeStr,
                    presetPublishType: publishTypeStr,
                })

                // 根据发布类型设置页面标题
                let title = '发布互助'
                if (publishTypeStr === '1') {
                    title = '发布需求'
                } else if (publishTypeStr === '2') {
                    title = '提供服务'
                }

                wx.setNavigationBarTitle({
                    title: title,
                })
            }

            // 如果有预设的分类，设置到表单数据中
            if (options && options.category) {
                this.setData({
                    'formData.category': options.category,
                    presetCategory: options.category,
                })
            }

            this.loadCategories().then(() => {
                // 分类数据加载完成后，根据发布类型更新分类
                this.updateCategoriesByPublishType()

                // 如果有预设分类，设置选中状态并自动填充content
                if (options && options.category) {
                    const categories = this.getCurrentCategories()
                    const category = categories.find(
                        (item) => item.dictValue === options.category
                    )
                    if (category) {
                        let updateData = {
                            selectedCategory: category,
                        }

                        // 预设分类时自动填充content
                        if (category.remark) {
                            updateData['formData.content'] = category.remark
                            updateData.isContentAutoFilled = true
                        }

                        this.setData(updateData)
                    }
                }
            })
            this.loadPublishTypes()
            this.fetchUserContacts()
        }

        this.initFormValidation()
    },

    /**
     * 加载互助详情（编辑模式）
     */
    async loadHelpDetail(helpId) {
        try {
            wx.showLoading({ title: '加载中...' })

            const response = await api.getCommunityHelpDetail(helpId)
            const helpDetail = response.data

            wx.hideLoading()

            // 处理图片数据
            let fileList = []
            if (helpDetail.images) {
                const images = helpDetail.images
                    .split(',')
                    .filter((img) => img.trim())
                fileList = images.map((img, index) => {
                    // 如果图片URL不是完整URL，添加文件服务器前缀
                    let imageUrl = img
                    if (!img.startsWith('http')) {
                        const systemInfo = wx.getStorageSync('systemInfo')
                        imageUrl = systemInfo?.fileUrl
                            ? systemInfo.fileUrl + img
                            : img
                    }
                    return {
                        url: imageUrl,
                        filePath: img, // 保存原始路径用于提交
                        isImage: true,
                    }
                })
            }

            // 解析并适配联系方式信息
            this.parseContactInfo(helpDetail.contactInfo)

            // 处理截止时间显示
            const endTimeDisplay = dateUtil.formatToDateOnly(helpDetail.endTime)

            // 设置表单数据
            this.setData({
                'formData.title': helpDetail.title || '',
                'formData.content': helpDetail.content || '',
                'formData.category': helpDetail.category || '',
                'formData.publishType': helpDetail.publishType
                    ? helpDetail.publishType.toString()
                    : '',
                'formData.endTime': helpDetail.endTime || '',
                fileList: fileList,
                titleLength: (helpDetail.title || '').length,
                endTimeDisplay: endTimeDisplay,
                // 编辑模式下，标记用户已经有内容，避免自动覆盖
                userModifiedContent: true,
                isContentAutoFilled: false,
            })

            // 根据发布类型更新分类数据
            this.updateCategoriesByPublishType()

            // 设置选中的分类
            if (helpDetail.category) {
                const categories = this.getCurrentCategories()
                const selectedCategory = categories.find(
                    (cat) => cat.dictValue === helpDetail.category
                )
                if (selectedCategory) {
                    this.setData({
                        selectedCategory: selectedCategory,
                    })
                }
            }

            // 设置选中的发布类型
            if (helpDetail.publishType && this.data.publishTypes.length > 0) {
                const selectedPublishType = this.data.publishTypes.find(
                    (type) =>
                        type.dictValue === helpDetail.publishType.toString()
                )
                if (selectedPublishType) {
                    this.setData({
                        selectedPublishType: selectedPublishType,
                    })
                }
            }

            // 重新验证表单
            this.checkCanSubmit()
        } catch (error) {
            wx.hideLoading()
            console.error('加载互助详情失败:', error)
            wx.showModal({
                title: '加载失败',
                content: '无法加载互助详情，请稍后重试',
                showCancel: false,
                success: () => {
                    wx.navigateBack()
                },
            })
        }
    },

    /**
     * 解析并适配联系方式信息
     */
    parseContactInfo(contactInfoJson) {
        console.log('=== 联系方式解析开始 ===');
        console.log('输入数据:', contactInfoJson);
        console.log('输入数据类型:', typeof contactInfoJson);

        const contactInfo = ContactUtils.parseContactInfoFromJson(contactInfoJson);
        console.log('解析结果:', contactInfo);

        this.setData({
            contactInfo
        });

        console.log('设置后的页面数据:', this.data.contactInfo);
        console.log('=== 联系方式解析结束 ===');

        // 更新表单验证状态
        this.initFormValidation();
    },

    /**
     * 加载分类列表
     */
    async loadCategories() {
        try {
            // 获取需求分类和服务分类数据
            const requireCategories =
                await systemInfoService.getCommunityHelpRequireCategories()
            const serviceCategories =
                await systemInfoService.getCommunityHelpServiceCategories()

            // 存储两种分类数据
            this.setData({
                requireCategories: requireCategories || [],
                serviceCategories: serviceCategories || [],
            })

            // 根据当前发布类型设置对应的分类数据
            this.updateCategoriesByPublishType()

            return Promise.resolve()
        } catch (error) {
            console.error('加载分类失败:', error)
            this.setData({
                requireCategories: [],
                serviceCategories: [],
                categoryColumns: [],
            })
            return Promise.resolve()
        }
    },

    /**
     * 根据发布类型获取对应的分类数据
     */
    getCurrentCategories() {
        const publishType = this.data.formData.publishType
        if (publishType === '1') {
            return this.data.requireCategories || []
        } else if (publishType === '2') {
            return this.data.serviceCategories || []
        }
        return []
    },

    /**
     * 根据发布类型更新分类数据
     */
    updateCategoriesByPublishType() {
        const categories = this.getCurrentCategories()
        console.log(categories)

        if (categories && categories.length > 0) {
            // 为Picker组件准备数据格式
            const categoryColumns = categories.map((item) => ({
                text: item.dictLabel,
                value: item.dictValue,
            }))

            this.setData({
                categoryColumns: categoryColumns,
            })
        } else {
            console.warn('当前发布类型无对应分类数据')
            this.setData({
                categoryColumns: [],
            })
        }
    },

    /**
     * 加载发布类型列表
     */
    async loadPublishTypes() {
        try {
            // 使用统一的系统信息服务获取发布类型数据
            const publishTypes =
                await systemInfoService.getCommunityHelpPublishTypes()

            if (publishTypes && publishTypes.length > 0) {
                this.setData({
                    publishTypes: publishTypes,
                })

                // 如果有预设的发布类型，设置选中的发布类型
                if (this.data.presetPublishType) {
                    // 直接使用字符串进行比较
                    const selectedPublishType = publishTypes.find(
                        (item) => item.dictValue === this.data.presetPublishType
                    )
                    if (selectedPublishType) {
                        this.setData({
                            selectedPublishType: selectedPublishType,
                        })
                    }
                }
            } else {
                console.warn('系统接口未返回发布类型数据')
                this.setData({
                    publishTypes: [],
                })
            }
            return Promise.resolve()
        } catch (error) {
            console.error('加载发布类型失败:', error)
            this.setData({
                publishTypes: [],
            })
            return Promise.resolve()
        }
    },

    /**
     * 标题输入
     */
    onTitleChange(event) {
        const title = event.detail.value || ''
        this.setData({
            'formData.title': title,
            titleLength: title.length,
        })
        this.checkCanSubmit()
    },

    /**
     * 内容输入
     */
    onContentChange(event) {
        this.setData({
            'formData.content': event.detail.value,
            userModifiedContent: true, // 标记用户手动修改了内容
        })
        this.checkCanSubmit()
    },

    /**
     * 获取用户联系方式
     */
    async fetchUserContacts() {
        try {
            const contactInfo = await ContactUtils.fetchUserContacts()
            this.setData({
                contactInfo,
            })
            // 更新表单验证状态
            this.initFormValidation()
        } catch (err) {
            console.error('获取联系方式失败:', err)
        }
    },

    /**
     * 显示联系方式选择器
     */
    showContactPicker() {
        this.setData({
            showContactPicker: true,
        })
    },

    /**
     * 关闭联系方式选择器
     */
    onCloseContactPicker() {
        this.setData({
            showContactPicker: false,
        })
    },

    /**
     * 处理联系方式变更
     */
    onContactChange(e) {
        const { contactInfo } = e.detail
        this.setData({
            contactInfo,
        })
        // 更新表单验证状态
        this.initFormValidation()
    },

    /**
     * 初始化表单验证
     */
    initFormValidation() {
        this.checkCanSubmit()
    },

    /**
     * 分类选择
     */
    onCategorySelect() {
        this.setData({
            showCategoryPicker: true,
        })
    },

    /**
     * 分类选择确认
     */
    onCategoryConfirm(event) {
        const { value, index } = event.detail
        // 从categoryColumns中获取选中的值
        const selectedValue = this.data.categoryColumns[index].value
        const categories = this.getCurrentCategories()
        const selectedCategory = categories.find(
            (item) => item.dictValue === selectedValue
        )

        // 检查是否需要自动填充content
        let updateData = {
            'formData.category': selectedValue,
            selectedCategory,
            showCategoryPicker: false,
        }

        // 自动填充content的条件：
        // 1. 用户没有手动修改过content，或者
        // 2. 这是第一次选择分类，或者
        // 3. 用户重新选择了不同的分类（编辑模式下允许重新填充）
        const shouldAutoFill = !this.data.userModifiedContent ||
                              !this.data.isContentAutoFilled ||
                              (this.data.selectedCategory.dictValue !== selectedValue)

        if (shouldAutoFill && selectedCategory && selectedCategory.remark) {
            updateData['formData.content'] = selectedCategory.remark
            updateData.isContentAutoFilled = true
            // 重置用户修改标记，因为这是系统自动填充的
            updateData.userModifiedContent = false
        }

        this.setData(updateData)
        this.checkCanSubmit()
    },

    /**
     * 关闭分类选择器
     */
    onCategoryPickerClose() {
        this.setData({
            showCategoryPicker: false,
        })
    },

    /**
     * 截止时间选择
     */
    onEndTimeSelect() {
        this.setData({
            showEndTimePicker: true,
        })
    },

    /**
     * 截止时间确认
     */
    onEndTimeConfirm(event) {
        // 参考活动发布页面的实现，直接使用 event.detail
        const timestamp = event.detail

        try {
            // 验证日期是否有效
            if (!dateUtil.isValidDate(timestamp)) {
                wx.showToast({
                    title: '时间格式错误',
                    icon: 'error',
                })
                return
            }

            // 格式化显示时间 (YYYY-MM-DD)，只显示到天
            const endTimeDisplay = dateUtil.formatToDateOnly(timestamp)

            // 设置为当天的23:59:59作为截止时间
            const endTimeValue = dateUtil.setToEndOfDay(timestamp)

            this.setData({
                'formData.endTime': endTimeValue,
                endTimeValue: timestamp,
                endTimeDisplay: endTimeDisplay,
                showEndTimePicker: false,
            })
            this.checkCanSubmit()
        } catch (error) {
            console.error('时间处理错误:', error)
            wx.showToast({
                title: '时间选择失败',
                icon: 'error',
            })
        }
    },

    /**
     * 关闭截止时间选择器
     */
    onEndTimePickerClose() {
        this.setData({
            showEndTimePicker: false,
        })
    },

    /**
     * 图片上传
     */
    async onImageUpload(event) {
        const { file } = event.detail
        // 转换为数组处理，支持单文件和多文件
        const files = Array.isArray(file) ? file : [file]

        // 检查文件总数量限制
        if (
            this.data.fileList.length + files.length >
            this.data.MAX_UPLOAD_COUNT
        ) {
            wx.showToast({
                title: `最多上传${this.data.MAX_UPLOAD_COUNT}个文件`,
                icon: 'none',
            })
            return
        }

        wx.showLoading({ title: '上传中...' })

        try {
            // 使用 Promise.all 同时上传多个文件
            const uploadTasks = files.map(async (file) => {
                const uploadedFile = await util.uploadFile({
                    filePath: file.url,
                    type: '7', // 使用类型7表示互助图片
                })

                if (!uploadedFile || !uploadedFile.filePath) {
                    throw new Error('文件上传失败，未获取到文件路径')
                }

                return {
                    url: file.url,
                    name: '互助图片',
                    isImage: true,
                    filePath: uploadedFile.filePath,
                }
            })

            const uploadedFiles = await Promise.all(uploadTasks)

            // 更新文件列表，添加所有上传成功的文件
            this.setData({
                fileList: [...this.data.fileList, ...uploadedFiles],
            })

            wx.hideLoading()
            wx.showToast({
                title: '上传成功',
                icon: 'success',
                duration: 500,
            })
        } catch (error) {
            wx.hideLoading()
            console.error('图片上传失败:', error)
            wx.showToast({
                title: error.message || '上传失败',
                icon: 'none',
            })
        }
    },

    /**
     * 删除图片
     */
    onImageDelete(event) {
        const { index } = event.detail
        const fileList = this.data.fileList
        fileList.splice(index, 1)
        this.setData({ fileList })
    },

    /**
     * 检查是否可以提交
     */
    checkCanSubmit() {
        const { title, content, category, publishType, endTime } =
            this.data.formData
        const { contactInfo } = this.data

        // 添加联系方式验证 - 至少选择一种联系方式且设置为可见
        const isContactValid = ContactUtils.validateContactInfo(contactInfo)

        const canSubmit =
            title.trim() &&
            content.trim() &&
            category &&
            publishType &&
            endTime &&
            isContactValid
        this.setData({
            canSubmit,
            hasValidContact: isContactValid
        })
    },

    /**
     * 提交表单
     */
    onSubmit() {
        if (!this.data.canSubmit) {
            wx.showToast({
                title: '请完善信息',
                icon: 'none',
            })
            return
        }

        this.showPublishConfirm()
    },

    /**
     * 显示发布确认对话框
     */
    showPublishConfirm() {
        this.setData({
            showPublishDialog: true,
        })
    },

    /**
     * 关闭发布确认对话框
     */
    onPublishDialogClose() {
        this.setData({
            showPublishDialog: false,
        })
    },

    /**
     * 确认发布
     */
    onPublishConfirm() {
        this.setData({
            showPublishDialog: false,
        })
        this.submitForm()
    },

    /**
     * 提交表单
     */
    async submitForm() {
        if (this.data.submitLoading) return

        this.setData({ submitLoading: true })

        try {
            // 使用工具类构建联系方式JSON数据
            const contactInfoJson = ContactUtils.formatContactInfoForSubmit(
                this.data.contactInfo
            )

            // 处理图片数据 - 使用服务器返回的filePath，存储为逗号分隔的字符串
            const images = this.data.fileList.map(
                (file) => file.filePath || file.url
            )

            const submitData = {
                title: this.data.formData.title,
                content: this.data.formData.content,
                category: this.data.formData.category,
                publishType: this.data.formData.publishType,
                endTime: this.data.formData.endTime,
                images: images.join(','), // 改为逗号分隔的字符串格式
                contactInfo: contactInfoJson,
            }

            // 根据是否是编辑模式调用不同的API
            if (this.data.isEdit) {
                submitData.id = this.data.helpId
                await api.updateCommunityHelp(submitData)
                // 设置刷新标记，让详情页面知道需要刷新
                wx.setStorageSync('needRefreshHelpDetail', true)
            } else {
                await api.publishCommunityHelp(submitData)
            }

            this.setData({ submitLoading: false })

            wx.showToast({
                title: this.data.isEdit ? '更新成功' : '发布成功',
                icon: 'success',
            })

            setTimeout(() => {
                wx.navigateBack()
            }, 100)
        } catch (error) {
            this.setData({ submitLoading: false })
            console.error('发布失败:', error)

            wx.showToast({
                title: '发布失败，请稍后重试',
                icon: 'none',
            })
        }
    },
})
